#!/usr/bin/env python3
"""
Comprehensive Indian Stock Market Data Fetcher using Dhan API
Fetches and stores OHLCV data for multiple timeframes in normalized SQLite database
"""

import os
import time
import datetime
import pandas as pd
import numpy as np
import logging
import threading
import sqlite3
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from collections import deque
from typing import Dict, List, Optional, Tuple
import json

# Dhan API imports
try:
    from dhanhq import dhanhq
except ImportError:
    print("Please install dhanhq: pip install dhanhq")
    exit(1)

# Technical analysis library
try:
    import ta
except ImportError:
    print("Please install ta: pip install ta")
    exit(1)


class DhanMarketDataFetcher:
    """
    Comprehensive market data fetcher for Indian stock market using Dhan API
    """
    
    def __init__(self, db_path: str = "Dhan_db.db"):
        """Initialize the market data fetcher"""
        
        # Dhan API Configuration
        self.DHAN_CLIENT_CODE = "1105577608"
        self.DHAN_TOKEN_ID = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"
        
        # Database configuration
        self.db_path = db_path
        self.db_lock = threading.Lock()
        
        # API rate limiting
        self.api_call_queue = deque()
        self.max_calls_per_minute = 100  # Adjust based on Dhan API limits
        self.call_interval = 60 / self.max_calls_per_minute
        
        # Time intervals mapping
        self.time_intervals = {
            '1min': {'table': 'market_data_1min', 'dhan_interval': '1'},
            '5min': {'table': 'market_data_5min', 'dhan_interval': '5'},
            '60min': {'table': 'market_data_60min', 'dhan_interval': '60'}
        }
        
        # NIFTY50 stocks configuration (initial target)
        self.nifty50_stocks = [
            {'symbol': 'RELIANCE', 'security_id': '1333', 'exchange': 'NSE'},
            {'symbol': 'TCS', 'security_id': '11536', 'exchange': 'NSE'},
            {'symbol': 'HDFCBANK', 'security_id': '1333', 'exchange': 'NSE'},
            {'symbol': 'INFY', 'security_id': '408', 'exchange': 'NSE'},
            {'symbol': 'ICICIBANK', 'security_id': '4963', 'exchange': 'NSE'},
            # Add more NIFTY50 stocks as needed
        ]
        
        # Initialize logging
        self.setup_logging()
        
        # Initialize Dhan API client
        self.dhan_client = None
        self.initialize_dhan_client()
        
        # Initialize database
        self.initialize_database()
    
    def setup_logging(self):
        """Setup comprehensive logging configuration"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('dhan_market_data.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("Dhan Market Data Fetcher initialized")
    
    def initialize_dhan_client(self):
        """Initialize Dhan API client with authentication"""
        try:
            self.dhan_client = dhanhq(self.DHAN_CLIENT_CODE, self.DHAN_TOKEN_ID)
            self.logger.info("Dhan API client initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Dhan API client: {e}")
            raise
    
    def initialize_database(self):
        """Initialize database connection and enable foreign keys"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                conn.commit()
            self.logger.info("Database initialized with foreign keys enabled")
        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise
    
    def clear_all_data(self):
        """Clear all existing data from database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = OFF")
                
                # Clear market data tables first (due to foreign key constraints)
                for interval in self.time_intervals:
                    table_name = self.time_intervals[interval]['table']
                    conn.execute(f"DELETE FROM {table_name}")
                    self.logger.info(f"Cleared data from {table_name}")
                
                # Clear instrument master table
                conn.execute("DELETE FROM Instrument_Master")
                self.logger.info("Cleared data from Instrument_Master")
                
                # Reset auto-increment sequences
                conn.execute("DELETE FROM sqlite_sequence")
                
                conn.execute("PRAGMA foreign_keys = ON")
                conn.commit()
                
            self.logger.info("All database tables cleared successfully")
        except Exception as e:
            self.logger.error(f"Failed to clear database: {e}")
            raise
    
    def rate_limit_api_call(self):
        """Implement rate limiting for API calls"""
        current_time = time.time()
        
        # Remove old calls outside the time window
        while self.api_call_queue and current_time - self.api_call_queue[0] > 60:
            self.api_call_queue.popleft()
        
        # Check if we need to wait
        if len(self.api_call_queue) >= self.max_calls_per_minute:
            sleep_time = 60 - (current_time - self.api_call_queue[0])
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        # Add current call to queue
        self.api_call_queue.append(current_time)
    
    def get_or_create_instrument(self, symbol: str, security_id: str, exchange: str, 
                                description: str = None, sector: str = None) -> int:
        """Get existing instrument_id or create new instrument in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                cursor = conn.cursor()
                
                # Check if instrument exists
                cursor.execute(
                    "SELECT instrument_id FROM Instrument_Master WHERE instrument_name = ?",
                    (symbol,)
                )
                result = cursor.fetchone()
                
                if result:
                    return result[0]
                
                # Create new instrument
                cursor.execute("""
                    INSERT INTO Instrument_Master 
                    (instrument_name, Security_Id, instrument_description, exchange, sector)
                    VALUES (?, ?, ?, ?, ?)
                """, (symbol, security_id, description, exchange, sector))
                
                instrument_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"Created new instrument: {symbol} with ID {instrument_id}")
                return instrument_id
                
        except Exception as e:
            self.logger.error(f"Failed to get/create instrument {symbol}: {e}")
            raise
