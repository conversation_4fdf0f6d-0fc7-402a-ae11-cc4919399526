#!/usr/bin/env python3
"""
Comprehensive Indian Stock Market Data Fetcher using Dhan API
Fetches and stores OHLCV data for multiple timeframes in normalized SQLite database
"""

import os
import time
import datetime
import pandas as pd
import numpy as np
import logging
import threading
import sqlite3
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from collections import deque
from typing import Dict, List, Optional, Tuple
import json

# Dhan API imports
try:
    from dhanhq import dhanhq
except ImportError:
    print("Please install dhanhq: pip install dhanhq")
    exit(1)

# Technical analysis library
try:
    import ta
except ImportError:
    print("Please install ta: pip install ta")
    exit(1)


class DhanMarketDataFetcher:
    """
    Comprehensive market data fetcher for Indian stock market using Dhan API
    """

    def __init__(self, config_path: str = "config.json", db_path: str = None):
        """Initialize the market data fetcher"""

        # Load configuration
        self.config = self.load_config(config_path)

        # Dhan API Configuration
        self.DHAN_CLIENT_CODE = self.config['dhan_api']['client_code']
        self.DHAN_TOKEN_ID = self.config['dhan_api']['token_id']

        # Database configuration
        self.db_path = db_path or self.config['database']['path']
        self.db_lock = threading.Lock()

        # API rate limiting
        self.api_call_queue = deque()
        self.max_calls_per_minute = self.config['api_limits']['max_calls_per_minute']
        self.call_interval = 60 / self.max_calls_per_minute
        self.max_workers = self.config['api_limits']['max_workers']

        # Time intervals mapping
        self.time_intervals = {
            '1min': {'table': 'market_data_1min', 'dhan_interval': '1'},
            '5min': {'table': 'market_data_5min', 'dhan_interval': '5'},
            '60min': {'table': 'market_data_60min', 'dhan_interval': '60'}
        }

        # Load stocks from configuration
        self.nifty50_stocks = self.config['nifty50_stocks']
        self.historical_days = self.config['data_settings']['historical_days']

        # Initialize logging
        self.setup_logging()

        # Initialize Dhan API client
        self.dhan_client = None
        self.initialize_dhan_client()

        # Initialize database
        self.initialize_database()

    def load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            print(f"Configuration file {config_path} not found. Using default settings.")
            return self.get_default_config()
        except json.JSONDecodeError as e:
            print(f"Error parsing configuration file: {e}. Using default settings.")
            return self.get_default_config()

    def get_default_config(self) -> Dict:
        """Get default configuration if config file is not available"""
        return {
            'dhan_api': {
                'client_code': "1105577608",
                'token_id': "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"
            },
            'database': {'path': "Dhan_db.db"},
            'api_limits': {'max_calls_per_minute': 100, 'max_workers': 5},
            'data_settings': {'historical_days': 365},
            'nifty50_stocks': [
                {'symbol': 'RELIANCE', 'security_id': '1333', 'exchange': 'NSE', 'sector': 'Oil & Gas'},
                {'symbol': 'TCS', 'security_id': '11536', 'exchange': 'NSE', 'sector': 'IT Services'},
                {'symbol': 'HDFCBANK', 'security_id': '1333', 'exchange': 'NSE', 'sector': 'Banking'},
                {'symbol': 'INFY', 'security_id': '408', 'exchange': 'NSE', 'sector': 'IT Services'},
                {'symbol': 'ICICIBANK', 'security_id': '4963', 'exchange': 'NSE', 'sector': 'Banking'},
            ]
        }
    
    def setup_logging(self):
        """Setup comprehensive logging configuration"""
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('dhan_market_data.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("Dhan Market Data Fetcher initialized")
    
    def initialize_dhan_client(self):
        """Initialize Dhan API client with authentication"""
        try:
            self.dhan_client = dhanhq(self.DHAN_CLIENT_CODE, self.DHAN_TOKEN_ID)
            self.logger.info("Dhan API client initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Dhan API client: {e}")
            raise
    
    def initialize_database(self):
        """Initialize database connection and enable foreign keys"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                conn.commit()
            self.logger.info("Database initialized with foreign keys enabled")
        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise
    
    def clear_all_data(self):
        """Clear all existing data from database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = OFF")
                
                # Clear market data tables first (due to foreign key constraints)
                for interval in self.time_intervals:
                    table_name = self.time_intervals[interval]['table']
                    conn.execute(f"DELETE FROM {table_name}")
                    self.logger.info(f"Cleared data from {table_name}")
                
                # Clear instrument master table
                conn.execute("DELETE FROM Instrument_Master")
                self.logger.info("Cleared data from Instrument_Master")
                
                # Reset auto-increment sequences
                conn.execute("DELETE FROM sqlite_sequence")
                
                conn.execute("PRAGMA foreign_keys = ON")
                conn.commit()
                
            self.logger.info("All database tables cleared successfully")
        except Exception as e:
            self.logger.error(f"Failed to clear database: {e}")
            raise
    
    def rate_limit_api_call(self):
        """Implement rate limiting for API calls"""
        current_time = time.time()
        
        # Remove old calls outside the time window
        while self.api_call_queue and current_time - self.api_call_queue[0] > 60:
            self.api_call_queue.popleft()
        
        # Check if we need to wait
        if len(self.api_call_queue) >= self.max_calls_per_minute:
            sleep_time = 60 - (current_time - self.api_call_queue[0])
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        # Add current call to queue
        self.api_call_queue.append(current_time)
    
    def get_or_create_instrument(self, symbol: str, security_id: str, exchange: str, 
                                description: str = None, sector: str = None) -> int:
        """Get existing instrument_id or create new instrument in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                cursor = conn.cursor()
                
                # Check if instrument exists
                cursor.execute(
                    "SELECT instrument_id FROM Instrument_Master WHERE instrument_name = ?",
                    (symbol,)
                )
                result = cursor.fetchone()
                
                if result:
                    return result[0]
                
                # Create new instrument
                cursor.execute("""
                    INSERT INTO Instrument_Master 
                    (instrument_name, Security_Id, instrument_description, exchange, sector)
                    VALUES (?, ?, ?, ?, ?)
                """, (symbol, security_id, description, exchange, sector))
                
                instrument_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"Created new instrument: {symbol} with ID {instrument_id}")
                return instrument_id
                
        except Exception as e:
            self.logger.error(f"Failed to get/create instrument {symbol}: {e}")
            raise

    def get_latest_timestamp(self, instrument_id: int, interval: str) -> Optional[datetime.datetime]:
        """Get the latest timestamp for an instrument in a specific interval table"""
        try:
            table_name = self.time_intervals[interval]['table']
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"""
                    SELECT MAX(timestamp) FROM {table_name}
                    WHERE instrument_id = ?
                """, (instrument_id,))
                result = cursor.fetchone()

                if result[0]:
                    return datetime.datetime.strptime(result[0], '%Y-%m-%d %H:%M:%S')
                return None

        except Exception as e:
            self.logger.error(f"Failed to get latest timestamp for instrument {instrument_id}: {e}")
            return None

    def fetch_historical_data(self, symbol: str, security_id: str, exchange: str,
                            interval: str, from_date: str, to_date: str) -> Optional[pd.DataFrame]:
        """Fetch historical data from Dhan API"""
        try:
            self.rate_limit_api_call()

            # Map interval to Dhan API format
            dhan_interval = self.time_intervals[interval]['dhan_interval']

            # Fetch data from Dhan API
            response = self.dhan_client.historical_data(
                symbol=symbol,
                exchange_segment=exchange,
                instrument_type="EQUITY",
                expiry_code=0,
                from_date=from_date,
                to_date=to_date,
                interval=dhan_interval
            )

            if response['status'] == 'success' and 'data' in response:
                df = pd.DataFrame(response['data'])

                # Standardize column names
                if not df.empty:
                    df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                    df['timestamp'] = pd.to_datetime(df['timestamp'])

                    # Ensure proper data types
                    for col in ['open', 'high', 'low', 'close']:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    df['volume'] = pd.to_numeric(df['volume'], errors='coerce').astype(int)

                    self.logger.info(f"Fetched {len(df)} records for {symbol} ({interval})")
                    return df

            self.logger.warning(f"No data received for {symbol} ({interval})")
            return None

        except Exception as e:
            self.logger.error(f"Failed to fetch data for {symbol} ({interval}): {e}")
            return None

    def store_market_data(self, df: pd.DataFrame, instrument_id: int, interval: str):
        """Store market data in the appropriate table"""
        if df is None or df.empty:
            return

        try:
            table_name = self.time_intervals[interval]['table']

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                cursor = conn.cursor()

                # Prepare data for insertion
                records = []
                for _, row in df.iterrows():
                    records.append((
                        instrument_id,
                        float(row['open']),
                        float(row['high']),
                        float(row['low']),
                        float(row['close']),
                        int(row['volume']),
                        row['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                    ))

                # Insert data with conflict resolution
                cursor.executemany(f"""
                    INSERT OR IGNORE INTO {table_name}
                    (instrument_id, open, high, low, close, volume, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, records)

                rows_inserted = cursor.rowcount
                conn.commit()

                self.logger.info(f"Stored {rows_inserted} records in {table_name} for instrument {instrument_id}")

        except Exception as e:
            self.logger.error(f"Failed to store market data: {e}")
            raise

    def fetch_and_store_instrument_data(self, stock_config: Dict, interval: str,
                                      is_initial_load: bool = False) -> bool:
        """Fetch and store data for a single instrument and interval"""
        try:
            symbol = stock_config['symbol']
            security_id = stock_config['security_id']
            exchange = stock_config['exchange']

            # Get or create instrument
            instrument_id = self.get_or_create_instrument(
                symbol=symbol,
                security_id=security_id,
                exchange=exchange,
                description=f"{symbol} - {exchange}",
                sector="Unknown"  # Can be enhanced with sector mapping
            )

            # Determine date range
            if is_initial_load:
                # For initial load, fetch maximum available data
                to_date = datetime.datetime.now()
                from_date = to_date - datetime.timedelta(days=self.historical_days)
            else:
                # For incremental updates, fetch from last timestamp
                latest_timestamp = self.get_latest_timestamp(instrument_id, interval)
                if latest_timestamp:
                    from_date = latest_timestamp + datetime.timedelta(minutes=1)
                    to_date = datetime.datetime.now()

                    # If we're already up to date, skip
                    if from_date >= to_date:
                        self.logger.info(f"{symbol} ({interval}) is already up to date")
                        return True
                else:
                    # No existing data, treat as initial load
                    to_date = datetime.datetime.now()
                    from_date = to_date - datetime.timedelta(days=365)

            # Format dates for API
            from_date_str = from_date.strftime('%Y-%m-%d')
            to_date_str = to_date.strftime('%Y-%m-%d')

            # Fetch data
            df = self.fetch_historical_data(
                symbol=symbol,
                security_id=security_id,
                exchange=exchange,
                interval=interval,
                from_date=from_date_str,
                to_date=to_date_str
            )

            # Store data
            if df is not None and not df.empty:
                self.store_market_data(df, instrument_id, interval)
                return True

            return False

        except Exception as e:
            self.logger.error(f"Failed to fetch/store data for {stock_config['symbol']} ({interval}): {e}")
            return False

    def fetch_all_data_parallel(self, is_initial_load: bool = False, max_workers: int = 5):
        """Fetch data for all instruments and intervals using parallel processing"""
        self.logger.info(f"Starting {'initial' if is_initial_load else 'incremental'} data fetch")

        # Create tasks for all combinations of stocks and intervals
        tasks = []
        for stock_config in self.nifty50_stocks:
            for interval in self.time_intervals.keys():
                tasks.append((stock_config, interval, is_initial_load))

        successful_tasks = 0
        failed_tasks = 0

        # Execute tasks in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(
                    self.fetch_and_store_instrument_data,
                    task[0], task[1], task[2]
                ): task for task in tasks
            }

            # Process completed tasks
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                stock_symbol = task[0]['symbol']
                interval = task[1]

                try:
                    success = future.result()
                    if success:
                        successful_tasks += 1
                        self.logger.info(f"✓ Completed: {stock_symbol} ({interval})")
                    else:
                        failed_tasks += 1
                        self.logger.warning(f"✗ Failed: {stock_symbol} ({interval})")

                except Exception as e:
                    failed_tasks += 1
                    self.logger.error(f"✗ Exception for {stock_symbol} ({interval}): {e}")

        self.logger.info(f"Data fetch completed: {successful_tasks} successful, {failed_tasks} failed")
        return successful_tasks, failed_tasks

    def get_database_stats(self) -> Dict:
        """Get statistics about the current database state"""
        stats = {}

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get instrument count
                cursor.execute("SELECT COUNT(*) FROM Instrument_Master")
                stats['total_instruments'] = cursor.fetchone()[0]

                # Get data counts for each interval
                for interval, config in self.time_intervals.items():
                    table_name = config['table']
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    stats[f'{interval}_records'] = cursor.fetchone()[0]

                    # Get date range for each table
                    cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table_name}")
                    min_date, max_date = cursor.fetchone()
                    stats[f'{interval}_date_range'] = (min_date, max_date)

                return stats

        except Exception as e:
            self.logger.error(f"Failed to get database stats: {e}")
            return {}

    def print_database_summary(self):
        """Print a summary of the database contents"""
        stats = self.get_database_stats()

        print("\n" + "="*60)
        print("DATABASE SUMMARY")
        print("="*60)
        print(f"Total Instruments: {stats.get('total_instruments', 0)}")
        print()

        for interval in self.time_intervals.keys():
            records = stats.get(f'{interval}_records', 0)
            date_range = stats.get(f'{interval}_date_range', (None, None))

            print(f"{interval.upper()} Data:")
            print(f"  Records: {records:,}")
            if date_range[0] and date_range[1]:
                print(f"  Date Range: {date_range[0]} to {date_range[1]}")
            print()

        print("="*60)

    def run_initial_load(self):
        """Run initial data load - clears existing data and fetches historical data"""
        self.logger.info("Starting initial data load...")

        # Clear existing data
        self.clear_all_data()

        # Fetch all historical data
        successful, failed = self.fetch_all_data_parallel(is_initial_load=True)

        # Print summary
        self.print_database_summary()

        self.logger.info(f"Initial load completed: {successful} successful, {failed} failed")
        return successful, failed

    def run_incremental_update(self):
        """Run incremental update - fetches only new data since last update"""
        self.logger.info("Starting incremental data update...")

        # Fetch new data only
        successful, failed = self.fetch_all_data_parallel(is_initial_load=False)

        # Print summary
        self.print_database_summary()

        self.logger.info(f"Incremental update completed: {successful} successful, {failed} failed")
        return successful, failed

    def add_custom_instruments(self, instruments: List[Dict]):
        """Add custom instruments to the fetching list"""
        for instrument in instruments:
            if all(key in instrument for key in ['symbol', 'security_id', 'exchange']):
                self.nifty50_stocks.append(instrument)
                self.logger.info(f"Added custom instrument: {instrument['symbol']}")
            else:
                self.logger.error(f"Invalid instrument config: {instrument}")


def main():
    """Main execution function"""
    print("Dhan Market Data Fetcher")
    print("========================")

    # Initialize fetcher
    fetcher = DhanMarketDataFetcher()

    # Menu for user choice
    while True:
        print("\nOptions:")
        print("1. Run Initial Load (Clear all data and fetch historical)")
        print("2. Run Incremental Update (Fetch only new data)")
        print("3. View Database Summary")
        print("4. Add Custom Instruments")
        print("5. Exit")

        choice = input("\nEnter your choice (1-5): ").strip()

        if choice == '1':
            fetcher.run_initial_load()
        elif choice == '2':
            fetcher.run_incremental_update()
        elif choice == '3':
            fetcher.print_database_summary()
        elif choice == '4':
            print("Custom instrument addition feature - implement as needed")
        elif choice == '5':
            print("Exiting...")
            break
        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    main()
