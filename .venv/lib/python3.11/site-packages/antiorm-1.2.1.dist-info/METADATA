Metadata-Version: 2.4
Name: antiorm
Version: 1.2.1
Summary: A Pythonic Helper for DBAPI-2.0 SQL Access
Home-page: http://furius.ca/antiorm
Download-URL: http://bitbucket.org/blais/antiorm
Author: <PERSON>
Author-email: <EMAIL>
License: GPL
License-File: COPYING
Dynamic: author
Dynamic: author-email
Dynamic: description
Dynamic: download-url
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: summary


Anti-ORM is not an ORM, and it certainly does not want to be.  Anti-ORM is a
simple Python module that provides a pythonic syntax for making it more
convenient to build SQL queries over the DBAPI-2.0 interface.

In practice, if you're the kind of person that likes it to the bare metal, it's
almost as good as the ORMs.  At least there is no magic, and it just works.
