# Dhan Market Data Fetcher

A comprehensive Python program for fetching and storing Indian stock market data using the Dhan API with a normalized SQLite database.

## Features

- **Multi-timeframe Data**: Fetches 1-minute, 5-minute, and 60-minute OHLCV data
- **Normalized Database**: Uses foreign key relationships for efficient storage
- **Parallel Processing**: Multi-threaded data fetching for improved performance
- **Incremental Updates**: Smart top-up mechanism to fetch only new data
- **Rate Limiting**: Built-in API rate limiting to respect Dhan API limits
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Configurable**: Easy configuration through JSON file
- **Error Handling**: Robust error handling for API failures and database operations

## Database Schema

The program uses a normalized SQLite database with the following structure:

### Instrument_Master Table
- `instrument_id` (PRIMARY KEY)
- `instrument_name` (UNIQUE)
- `Security_Id` (UNIQUE)
- `instrument_description`
- `exchange`
- `sector`
- `created_at`

### Market Data Tables (1min, 5min, 60min)
- `id` (PRIMARY KEY)
- `instrument_id` (FOREIGN KEY)
- `open`, `high`, `low`, `close` (REAL)
- `volume` (INTEGER)
- `timestamp` (DATETIME)
- UNIQUE constraint on (instrument_id, timestamp)

## Installation

1. **Clone or download the files**:
   ```bash
   # Ensure you have these files:
   # - dhan_market_data_fetcher.py
   # - config.json
   # - requirements.txt
   # - Dhan_db.db (SQLite database)
   ```

2. **Install required packages**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify setup**:
   ```bash
   python test_setup.py
   ```

## Configuration

Edit `config.json` to customize:

```json
{
    "dhan_api": {
        "client_code": "YOUR_CLIENT_CODE",
        "token_id": "YOUR_TOKEN_ID"
    },
    "database": {
        "path": "Dhan_db.db"
    },
    "api_limits": {
        "max_calls_per_minute": 100,
        "max_workers": 5
    },
    "data_settings": {
        "historical_days": 365
    },
    "nifty50_stocks": [
        {
            "symbol": "RELIANCE",
            "security_id": "1333",
            "exchange": "NSE",
            "sector": "Oil & Gas"
        }
    ]
}
```

## Usage

### Command Line Interface

```bash
python dhan_market_data_fetcher.py
```

This will present a menu with options:
1. **Initial Load**: Clear all data and fetch historical data
2. **Incremental Update**: Fetch only new data since last update
3. **View Database Summary**: Display current database statistics
4. **Add Custom Instruments**: Add new instruments to track
5. **Exit**: Close the program

### Programmatic Usage

```python
from dhan_market_data_fetcher import DhanMarketDataFetcher

# Initialize fetcher
fetcher = DhanMarketDataFetcher()

# Run initial load
fetcher.run_initial_load()

# Or run incremental update
fetcher.run_incremental_update()

# Get database statistics
stats = fetcher.get_database_stats()
print(stats)
```

## Data Flow

1. **Instrument Management**: 
   - Instruments are stored in `Instrument_Master` table
   - Each instrument gets a unique `instrument_id`

2. **Data Fetching**:
   - Parallel fetching for multiple instruments and timeframes
   - Rate limiting to respect API limits
   - Error handling and retry logic

3. **Data Storage**:
   - Market data stored with foreign key references
   - Duplicate prevention using unique constraints
   - Efficient indexing for fast queries

4. **Incremental Updates**:
   - Checks latest timestamp for each instrument/timeframe
   - Fetches only new data since last update
   - Prevents duplicate entries

## API Rate Limiting

The program implements intelligent rate limiting:
- Configurable calls per minute limit
- Automatic throttling when approaching limits
- Queue-based call tracking

## Logging

Comprehensive logging to both file and console:
- `dhan_market_data.log`: Detailed log file
- Console output: Real-time progress updates
- Different log levels: INFO, WARNING, ERROR

## Error Handling

- **API Failures**: Retry logic and graceful degradation
- **Database Errors**: Transaction rollback and error reporting
- **Network Issues**: Timeout handling and reconnection
- **Data Validation**: Type checking and data cleaning

## Performance Optimization

- **Parallel Processing**: Multi-threaded data fetching
- **Database Indexing**: Optimized indexes for fast queries
- **Batch Operations**: Bulk inserts for efficiency
- **Memory Management**: Efficient data processing

## Troubleshooting

### Common Issues

1. **Import Errors**:
   ```bash
   pip install dhanhq pandas numpy ta
   ```

2. **Database Locked**:
   - Ensure no other processes are using the database
   - Check file permissions

3. **API Authentication**:
   - Verify client_code and token_id in config.json
   - Check token expiration

4. **Rate Limiting**:
   - Reduce max_workers in config.json
   - Increase max_calls_per_minute if API allows

### Debug Mode

Enable detailed logging by modifying the logging level:
```python
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

To add new features or instruments:

1. **Add New Instruments**: Update `config.json`
2. **Modify Time Intervals**: Update `time_intervals` in the code
3. **Enhance Data Processing**: Add technical indicators using `ta` library

## License

This project is for educational and personal use. Please ensure compliance with Dhan API terms of service.

## Support

For issues and questions:
1. Check the log files for detailed error messages
2. Run `test_setup.py` to verify configuration
3. Ensure all dependencies are installed correctly
