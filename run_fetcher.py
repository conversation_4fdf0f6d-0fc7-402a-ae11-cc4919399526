#!/usr/bin/env python3
"""
Launcher script for Dhan Market Data Fetcher
This ensures we use the correct Python interpreter with all dependencies
"""

import subprocess
import sys
import os

def main():
    """Launch the main fetcher program with the correct Python interpreter"""
    
    # Try to find the correct Python interpreter
    python_interpreters = [
        "/usr/bin/python3",  # System Python
        "/usr/local/bin/python3",  # Homebrew Python
        "python3",  # PATH Python
        "python"  # Fallback
    ]
    
    working_python = None
    
    for python_cmd in python_interpreters:
        try:
            # Test if this Python has the required packages
            result = subprocess.run([
                python_cmd, "-c", 
                "import pandas, numpy, dhanhq, ta; print('OK')"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and "OK" in result.stdout:
                working_python = python_cmd
                print(f"Using Python interpreter: {python_cmd}")
                break
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            continue
    
    if not working_python:
        print("Error: Could not find a Python interpreter with required packages.")
        print("Please install the required packages:")
        print("  pip3 install pandas numpy dhanhq ta")
        return 1
    
    # Launch the main program
    try:
        script_path = os.path.join(os.path.dirname(__file__), "dhan_market_data_fetcher.py")
        subprocess.run([working_python, script_path], check=True)
        return 0
    except subprocess.CalledProcessError as e:
        print(f"Error running the fetcher: {e}")
        return 1
    except KeyboardInterrupt:
        print("\nProgram interrupted by user.")
        return 0

if __name__ == "__main__":
    sys.exit(main())
