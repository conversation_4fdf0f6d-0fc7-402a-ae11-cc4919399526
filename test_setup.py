#!/usr/bin/env python3
"""
Test script to verify the Dhan Market Data Fetcher setup
"""

import sys
import sqlite3
import json
from pathlib import Path

def test_database_connection():
    """Test database connection and schema"""
    try:
        with sqlite3.connect("Dhan_db.db") as conn:
            cursor = conn.cursor()
            
            # Test if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['Instrument_Master', 'market_data_1min', 'market_data_5min', 'market_data_60min']
            
            print("Database Connection: ✓")
            print(f"Tables found: {tables}")
            
            for table in expected_tables:
                if table in tables:
                    print(f"  {table}: ✓")
                else:
                    print(f"  {table}: ✗ MISSING")
                    return False
            
            return True
            
    except Exception as e:
        print(f"Database Connection: ✗ ERROR - {e}")
        return False

def test_config_file():
    """Test configuration file"""
    try:
        with open("config.json", 'r') as f:
            config = json.load(f)
        
        required_keys = ['dhan_api', 'database', 'api_limits', 'data_settings', 'nifty50_stocks']
        
        print("Configuration File: ✓")
        
        for key in required_keys:
            if key in config:
                print(f"  {key}: ✓")
            else:
                print(f"  {key}: ✗ MISSING")
                return False
        
        print(f"  Configured stocks: {len(config['nifty50_stocks'])}")
        return True
        
    except Exception as e:
        print(f"Configuration File: ✗ ERROR - {e}")
        return False

def test_imports():
    """Test required imports"""
    imports_status = {}
    
    try:
        import pandas
        imports_status['pandas'] = f"✓ {pandas.__version__}"
    except ImportError:
        imports_status['pandas'] = "✗ MISSING"
    
    try:
        import numpy
        imports_status['numpy'] = f"✓ {numpy.__version__}"
    except ImportError:
        imports_status['numpy'] = "✗ MISSING"
    
    try:
        import dhanhq
        imports_status['dhanhq'] = "✓ Available"
    except ImportError:
        imports_status['dhanhq'] = "✗ MISSING - Install with: pip install dhanhq"
    
    try:
        import ta
        imports_status['ta'] = "✓ Available"
    except ImportError:
        imports_status['ta'] = "✗ MISSING - Install with: pip install ta"
    
    print("Required Imports:")
    for package, status in imports_status.items():
        print(f"  {package}: {status}")
    
    return all("✓" in status for status in imports_status.values())

def test_main_script():
    """Test if main script can be imported"""
    try:
        # Test if the main script exists and can be parsed
        with open("dhan_market_data_fetcher.py", 'r') as f:
            content = f.read()
        
        if "class DhanMarketDataFetcher" in content:
            print("Main Script: ✓")
            return True
        else:
            print("Main Script: ✗ DhanMarketDataFetcher class not found")
            return False
            
    except Exception as e:
        print(f"Main Script: ✗ ERROR - {e}")
        return False

def main():
    """Run all tests"""
    print("Dhan Market Data Fetcher - Setup Verification")
    print("=" * 50)
    
    tests = [
        ("Database Schema", test_database_connection),
        ("Configuration File", test_config_file),
        ("Required Imports", test_imports),
        ("Main Script", test_main_script)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        result = test_func()
        results.append(result)
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    
    for i, (test_name, _) in enumerate(tests):
        status = "PASS" if results[i] else "FAIL"
        print(f"  {test_name}: {status}")
    
    if all(results):
        print("\n✓ All tests passed! Setup is ready.")
        print("\nNext steps:")
        print("1. Run: python dhan_market_data_fetcher.py")
        print("2. Choose option 1 for initial data load")
        return 0
    else:
        print("\n✗ Some tests failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
